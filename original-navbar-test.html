<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Original Navbar Test - Dwelling</title>
    <link rel="stylesheet" href="css/style.css">
    
    <style>
        /* Test content styles */
        .test-content {
            padding: 50px 20px;
            text-align: center;
            background: #f8f9fa;
            min-height: 500px;
        }
        
        .status-box {
            background: white;
            padding: 30px;
            margin: 20px auto;
            max-width: 800px;
            border-radius: 15px;
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
        }
        
        .status-box h2 {
            color: #B9224B;
            margin-bottom: 20px;
        }
        
        .status-list {
            text-align: left;
            line-height: 2;
        }
        
        .status-good {
            color: green;
            font-weight: bold;
        }
        
        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-top: 30px;
        }
        
        .feature-card {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 3px 15px rgba(0,0,0,0.1);
        }
        
        .feature-card h3 {
            color: #B9224B;
            margin-bottom: 10px;
        }
    </style>
</head>
<body>
    <!-- Original Navbar Structure -->
    <div class="container">
        <nav>
            <div class="logo">
                <img src="assets/logo.png" alt="Logo" onerror="this.style.display='none'; this.nextElementSibling.style.display='block';">
                <div style="display:none; width:100%; height:100%; background:#B9224B; border-radius:50%; display:flex; align-items:center; justify-content:center; color:white; font-weight:bold; font-size:24px;">D</div>
            </div>
            <ul class="nav-links">
                <li><a href="#">Home</a></li>
                <li><a href="#">About Us</a></li>
                <li class="dropdown">
                    <a href="#" class="dropdown-toggle">Services</a>
                    <ul class="dropdown-menu">
                        <li><a href="#">Buy Property</a></li>
                        <li><a href="#">Lease Property</a></li>
                        <li><a href="#">Pre-Lease</a></li>
                        <li><a href="#">Sell Property</a></li>
                        <li><a href="#">Investment</a></li>
                    </ul>
                </li>
                <li class="dropdown">
                    <a href="#" class="dropdown-toggle">Projects</a>
                    <ul class="dropdown-menu">
                        <li><a href="#">New Launch</a></li>
                        <li><a href="#">Resale</a></li>
                    </ul>
                </li>
                <li><a href="#">Insight</a></li>
                <li><a href="#">Calculator</a></li>
                <li><a href="#">Career</a></li>
            </ul>
            <div class="btn">
                <a href="#">Let's Connect</a>
            </div>
            <!-- Mobile Menu Button -->
            <div class="mobile-menu-btn" id="mobileMenuBtn">
                <span></span>
                <span></span>
                <span></span>
            </div>

            <!-- Mobile Menu Overlay -->
            <div class="mobile-menu-overlay" id="mobileMenuOverlay">
                <div class="mobile-menu">
                    <div class="mobile-menu-header">
                        <div class="mobile-logo">
                            <img src="assets/logo.png" alt="Logo" onerror="this.style.display='none'; this.nextElementSibling.style.display='block';">
                            <div style="display:none; width:100%; height:100%; background:#B9224B; border-radius:50%; display:flex; align-items:center; justify-content:center; color:white; font-weight:bold; font-size:18px;">D</div>
                        </div>
                        <button class="mobile-close-btn" id="mobileCloseBtn">&times;</button>
                    </div>
                    <ul class="mobile-nav-links">
                        <li><a href="#">Home</a></li>
                        <li><a href="#">About Us</a></li>
                        <li class="mobile-dropdown">
                            <a href="#" class="mobile-dropdown-toggle">Services</a>
                            <ul class="mobile-dropdown-menu">
                                <li><a href="#">Buy Property</a></li>
                                <li><a href="#">Lease Property</a></li>
                                <li><a href="#">Pre-Lease</a></li>
                                <li><a href="#">Sell Property</a></li>
                                <li><a href="#">Investment</a></li>
                            </ul>
                        </li>
                        <li class="mobile-dropdown">
                            <a href="#" class="mobile-dropdown-toggle">Projects</a>
                            <ul class="mobile-dropdown-menu">
                                <li><a href="#">New Launch</a></li>
                                <li><a href="#">Resale</a></li>
                            </ul>
                        </li>
                        <li><a href="#">Insight</a></li>
                        <li><a href="#">Calculator</a></li>
                        <li><a href="#">Career</a></li>
                    </ul>
                    
                    <!-- Mobile Connect Section -->
                    <div class="mobile-connect-section">
                        <div class="mobile-connect-btn">
                            <a href="#">Let's Connect</a>
                        </div>
                        <div class="mobile-social-links">
                            <a href="https://www.facebook.com/share/1S9KHZS8PW/?mibextid=wwXIfr" class="social-link" aria-label="Facebook" target="_blank" rel="noopener noreferrer">
                                FB
                            </a>
                            <a href="https://www.instagram.com/dwellingdesire?igsh=ZmNyZHYwMXNiNjlr" class="social-link" aria-label="Instagram" target="_blank" rel="noopener noreferrer">
                                IG
                            </a>
                            <a href="https://www.linkedin.com/in/dhara-pujara-2b8443164?utm_source=share&utm_campaign=share_via&utm_content=profile&utm_medium=ios_app" class="social-link" aria-label="LinkedIn" target="_blank" rel="noopener noreferrer">
                                LI
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </nav>
    </div>

    <!-- Test Content -->
    <div class="test-content">
        <h1>✅ Navbar Successfully Reverted to Original State</h1>
        <p>Your navbar has been restored to its original clean design without Bootstrap conflicts.</p>
        
        <div class="status-box">
            <h2>Restoration Status</h2>
            <div class="status-list">
                <div><span class="status-good">✓</span> Removed Bootstrap CSS dependencies</div>
                <div><span class="status-good">✓</span> Restored original clean CSS structure</div>
                <div><span class="status-good">✓</span> Fixed dropdown menu functionality</div>
                <div><span class="status-good">✓</span> Preserved mobile menu with "Let's Connect" button</div>
                <div><span class="status-good">✓</span> Maintained social media links (FB, IG, LI)</div>
                <div><span class="status-good">✓</span> Removed Font Awesome dependencies</div>
                <div><span class="status-good">✓</span> Clean hamburger menu animation</div>
                <div><span class="status-good">✓</span> Original responsive design preserved</div>
            </div>
        </div>

        <div class="feature-grid">
            <div class="feature-card">
                <h3>Desktop Navigation</h3>
                <p>Clean text-only navigation with working dropdown menus on hover. No icons, just clean typography.</p>
            </div>
            
            <div class="feature-card">
                <h3>Mobile Menu</h3>
                <p>Slide-in mobile menu with hamburger animation, dropdown functionality, and connect button.</p>
            </div>
            
            <div class="feature-card">
                <h3>Social Links</h3>
                <p>Your real Facebook, Instagram, and LinkedIn links are integrated in the mobile menu footer.</p>
            </div>
            
            <div class="feature-card">
                <h3>Performance</h3>
                <p>Lightweight CSS without Bootstrap overhead. Fast loading and smooth animations.</p>
            </div>
        </div>

        <div class="status-box">
            <h2>Test Instructions</h2>
            <div class="status-list">
                <div><strong>Desktop:</strong> Hover over "Services" and "Projects" to test dropdown menus</div>
                <div><strong>Mobile:</strong> Resize window below 768px to see mobile menu button</div>
                <div><strong>Mobile Menu:</strong> Click hamburger button to open mobile menu</div>
                <div><strong>Dropdowns:</strong> Click on "Services" or "Projects" in mobile menu</div>
                <div><strong>Connect Button:</strong> Verify "Let's Connect" button is visible in mobile menu</div>
                <div><strong>Social Links:</strong> Test FB, IG, LI links in mobile menu footer</div>
            </div>
        </div>
    </div>

    <script src="js/mobile-menu.js"></script>
    
    <script>
        // Simple test to verify functionality
        document.addEventListener('DOMContentLoaded', function() {
            console.log('✅ Original Navbar Restored Successfully');
            console.log('✅ No Bootstrap conflicts');
            console.log('✅ Clean CSS structure');
            console.log('✅ Mobile menu functionality preserved');
            
            // Test dropdown presence
            const dropdowns = document.querySelectorAll('.dropdown-menu');
            console.log(`✅ Found ${dropdowns.length} dropdown menus`);
            
            // Test mobile menu
            const mobileMenu = document.querySelector('.mobile-menu-overlay');
            console.log('✅ Mobile menu:', mobileMenu ? 'Present' : 'Missing');
            
            // Test connect button in mobile menu
            const connectBtn = document.querySelector('.mobile-connect-btn');
            console.log('✅ Mobile connect button:', connectBtn ? 'Present' : 'Missing');
        });
    </script>
</body>
</html>
