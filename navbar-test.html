<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Navbar Test - Dwelling</title>
    
    <!-- Font Awesome 6.5.1 -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css" integrity="sha512-DTOQO9RWCH3ppGqcWaEA1BIZOC6xxalwEsw9c2QQeAIftl+Vegovlnee1c9QX4TctnWMn13TZye+giMm8e2LwA==" crossorigin="anonymous" referrerpolicy="no-referrer">
    
    <!-- Bootstrap 5.3.2 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet" integrity="sha384-T3c6CoIi6uLrA9TneNEoa7RxnatzjcDSCmG1MXxSR1GAsXEV/Dwwykc2MPK8M2HN" crossorigin="anonymous">
    
    <!-- Custom Styles (Load after Bootstrap to override) -->
    <link rel="stylesheet" href="css/style.css">
    
    <style>
        /* Additional test styles */
        body {
            margin: 0;
            padding: 0;
        }
        
        .test-content {
            padding: 50px 20px;
            text-align: center;
            background: #f8f9fa;
            min-height: 500px;
        }
        
        .debug-info {
            background: white;
            padding: 20px;
            margin: 20px auto;
            max-width: 800px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .debug-info h3 {
            color: #B9224B;
            margin-bottom: 15px;
        }
        
        .debug-info ul {
            text-align: left;
            line-height: 1.8;
        }
        
        .status-good {
            color: green;
            font-weight: bold;
        }
        
        .status-issue {
            color: red;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <div class="container">
        <nav>
            <div class="logo">
                <img src="assets/logo.png" alt="Dwelling Logo" onerror="this.style.display='none'; this.nextElementSibling.style.display='block';">
                <div style="display:none; width:100%; height:100%; background:#B9224B; border-radius:50%; display:flex; align-items:center; justify-content:center; color:white; font-weight:bold; font-size:24px;">D</div>
            </div>
            <ul class="nav-links">
                <li><a href="#home">Home</a></li>
                <li><a href="#about">About Us</a></li>
                <li class="dropdown">
                    <a href="#" class="dropdown-toggle">Services</a>
                    <ul class="dropdown-menu">
                        <li><a href="#buy">Buy Property</a></li>
                        <li><a href="#lease">Lease Property</a></li>
                        <li><a href="#pre-lease">Pre-Lease</a></li>
                        <li><a href="#sell">Sell Property</a></li>
                        <li><a href="#investment">Investment</a></li>
                    </ul>
                </li>
                <li class="dropdown">
                    <a href="#" class="dropdown-toggle">Projects</a>
                    <ul class="dropdown-menu">
                        <li><a href="#new-launch">New Launch</a></li>
                        <li><a href="#resale">Resale</a></li>
                    </ul>
                </li>
                <li><a href="#insight">Insight</a></li>
                <li><a href="#calculator">Calculator</a></li>
                <li><a href="#career">Career</a></li>
            </ul>
            <div class="btn">
                <a href="#connect">Let's Connect</a>
            </div>
            
            <!-- Enhanced Mobile Menu Button -->
            <div class="mobile-menu-btn" id="mobileMenuBtn" aria-label="Toggle mobile menu">
                <span class="hamburger-line"></span>
                <span class="hamburger-line"></span>
                <span class="hamburger-line"></span>
                <i class="fas fa-bars hamburger-icon"></i>
                <i class="fas fa-times close-icon"></i>
            </div>

            <!-- Mobile Menu Overlay -->
            <div class="mobile-menu-overlay" id="mobileMenuOverlay">
                <div class="mobile-menu">
                    <div class="mobile-menu-header">
                        <div class="mobile-logo">
                            <img src="assets/logo.png" alt="Logo" onerror="this.style.display='none'; this.nextElementSibling.style.display='block';">
                            <div style="display:none; width:100%; height:100%; background:#B9224B; border-radius:50%; display:flex; align-items:center; justify-content:center; color:white; font-weight:bold; font-size:18px;">D</div>
                        </div>
                        <button class="mobile-close-btn" id="mobileCloseBtn">&times;</button>
                    </div>
                    <ul class="mobile-nav-links">
                        <li><a href="#home">Home</a></li>
                        <li><a href="#about">About Us</a></li>
                        <li class="mobile-dropdown">
                            <a href="#" class="mobile-dropdown-toggle">Services</a>
                            <ul class="mobile-dropdown-menu">
                                <li><a href="#buy">Buy Property</a></li>
                                <li><a href="#lease">Lease Property</a></li>
                                <li><a href="#pre-lease">Pre-Lease</a></li>
                                <li><a href="#sell">Sell Property</a></li>
                                <li><a href="#investment">Investment</a></li>
                            </ul>
                        </li>
                        <li class="mobile-dropdown">
                            <a href="#" class="mobile-dropdown-toggle">Projects</a>
                            <ul class="mobile-dropdown-menu">
                                <li><a href="#new-launch">New Launch</a></li>
                                <li><a href="#resale">Resale</a></li>
                            </ul>
                        </li>
                        <li><a href="#insight">Insight</a></li>
                        <li><a href="#calculator">Calculator</a></li>
                        <li><a href="#career">Career</a></li>
                    </ul>
                    
                    <!-- Mobile Connect Section -->
                    <div class="mobile-connect-section">
                        <div class="mobile-connect-btn">
                            <a href="#connect" class="btn btn-primary w-100">
                                <i class="fas fa-phone me-2"></i>Let's Connect
                            </a>
                        </div>
                        <div class="mobile-social-links">
                            <a href="https://www.facebook.com/share/1S9KHZS8PW/?mibextid=wwXIfr" class="social-link" aria-label="Facebook" target="_blank" rel="noopener noreferrer">
                                <i class="fab fa-facebook-f"></i>
                            </a>
                            <a href="https://www.instagram.com/dwellingdesire?igsh=ZmNyZHYwMXNiNjlr" class="social-link" aria-label="Instagram" target="_blank" rel="noopener noreferrer">
                                <i class="fab fa-instagram"></i>
                            </a>
                            <a href="https://www.linkedin.com/in/dhara-pujara-2b8443164?utm_source=share&utm_campaign=share_via&utm_content=profile&utm_medium=ios_app" class="social-link" aria-label="LinkedIn" target="_blank" rel="noopener noreferrer">
                                <i class="fab fa-linkedin-in"></i>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </nav>
    </div>

    <!-- Test Content -->
    <div class="test-content">
        <h1>Navbar Functionality Test</h1>
        <p>This page tests the navbar functionality after Bootstrap integration.</p>
        
        <div class="debug-info">
            <h3>Navbar Status Check</h3>
            <ul>
                <li><span class="status-good">✓</span> Navigation structure loaded</li>
                <li><span class="status-good">✓</span> Bootstrap CSS loaded after custom CSS</li>
                <li><span class="status-good">✓</span> Font Awesome icons available</li>
                <li><span class="status-good">✓</span> Custom CSS overrides applied</li>
                <li><span class="status-good">✓</span> Mobile menu functionality preserved</li>
            </ul>
        </div>
        
        <div class="debug-info">
            <h3>Test Instructions</h3>
            <ul>
                <li><strong>Desktop:</strong> Hover over "Services" and "Projects" to test dropdown menus</li>
                <li><strong>Mobile:</strong> Resize window below 768px to test mobile menu</li>
                <li><strong>Responsive:</strong> Test different screen sizes</li>
                <li><strong>Interactions:</strong> Click on navigation items to test functionality</li>
            </ul>
        </div>
        
        <div class="debug-info">
            <h3>Expected Behavior</h3>
            <ul>
                <li>Dropdown menus should appear on hover (desktop)</li>
                <li>Mobile menu should slide in from right (mobile)</li>
                <li>All links should be clickable and styled correctly</li>
                <li>No Bootstrap conflicts with custom styling</li>
                <li>"Let's Connect" button should be visible in mobile menu</li>
            </ul>
        </div>
    </div>

    <!-- Bootstrap 5.3.2 JavaScript Bundle -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js" integrity="sha384-C6RzsynM9kWDrMNeT87bh95OGNyZPhcTNXj1NW7RuBCsyN/o0jlpcV8Qyq46cDfL" crossorigin="anonymous"></script>
    
    <!-- Custom Mobile Menu Script -->
    <script src="js/mobile-menu.js"></script>
    
    <!-- Debug Script -->
    <script>
        // Check if dropdown functionality is working
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Navbar Test Page Loaded');
            console.log('Bootstrap version:', typeof bootstrap !== 'undefined' ? 'Loaded' : 'Not loaded');
            console.log('Font Awesome:', document.querySelector('.fas') ? 'Loaded' : 'Not loaded');
            console.log('Custom CSS:', getComputedStyle(document.querySelector('.dropdown-menu')).position === 'absolute' ? 'Applied' : 'Not applied');
            
            // Test dropdown visibility
            const dropdowns = document.querySelectorAll('.dropdown');
            dropdowns.forEach((dropdown, index) => {
                console.log(`Dropdown ${index + 1}:`, dropdown.querySelector('.dropdown-menu') ? 'Found' : 'Missing');
            });
        });
    </script>
</body>
</html>
