@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap');
*{
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: 'Poppins', sans-serif;
}
a{
    text-decoration: none;
    color: black;
}
.container{
    width: 100%;
    /* height: 100vh;    */
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 50%, #e2e8f0 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: row;
}
nav{
    width: 100%;
    height: 80px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 50px;
    flex-direction: row;
}
.nav-links{
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: row;
    list-style: none;
    gap: 2rem;
}
.nav-links ul li a{
    text-decoration: none;
}
.logo{
    width: 100px;
    height: 100px;
    padding: 1rem;
}
.logo img{
    width: 100%;
    height: 100%;
    object-fit: contain;
}

/* Dropdown Styles */
.dropdown {
    position: relative;
}

.dropdown-toggle {
    cursor: pointer;
    transition: color 0.3s ease;
    display: flex;
    align-items: center;
    gap: 5px;
}

.dropdown-toggle::after {
    content: '';
    width: 0;
    height: 0;
    border-left: 5px solid transparent;
    border-right: 5px solid transparent;
    border-top: 5px solid black;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.1));
}

.dropdown-toggle:hover {
    color: #B9224B;
}

.dropdown:hover .dropdown-toggle::after {
    transform: rotate(180deg) scale(1.1);
    border-top-color: #B9224B;
    filter: drop-shadow(0 2px 4px rgba(185, 34, 75, 0.4));
}

.dropdown-menu {
    position: absolute;
    top: 100%;
    left: 0;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    min-width: 220px;
    box-shadow:
        0 20px 40px rgba(185, 34, 75, 0.1),
        0 8px 25px rgba(0, 0, 0, 0.08);
    border-radius: 16px;
    padding: 12px 0;
    list-style: none;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-15px) scale(0.95);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    z-index: 1000;
}

.dropdown:hover .dropdown-menu {
    opacity: 1;
    visibility: visible;
    transform: translateY(0) scale(1);
}

.dropdown-menu li {
    padding: 0;
}

.dropdown-menu li a {
    display: block;
    padding: 14px 24px;
    color: #4a5568;
    text-decoration: none;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    border-radius: 12px;
    margin: 2px 12px;
    font-weight: 500;
    font-size: 14px;
    position: relative;
    overflow: hidden;
}

.dropdown-menu li a::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    /* background: linear-gradient(90deg, transparent, rgba(185, 34, 75, 0.1), transparent); */
    transition: left 0.5s ease;
}

.dropdown-menu li a:hover {
    /* background: linear-gradient(135deg, rgba(185, 34, 75, 0.08), rgba(185, 34, 75, 0.04)); */
    color: #B9224B;
    /* transform: translateX(8px) scale(1.02);
    font-weight: 600; */
    /* box-shadow: 0 4px 12px rgba(185, 34, 75, 0.15); */
}

.dropdown-menu li a:hover::before {
    left: 100%;
}

/* Enhanced Mobile Menu Button */
.mobile-menu-btn {
    display: none;
    flex-direction: column;
    cursor: pointer;
    padding: 10px;
    border-radius: 12px;
    background: transparent;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
    border: 2px solid transparent;
    min-width: 48px;
    min-height: 48px;
    justify-content: center;
    align-items: center;
    -webkit-tap-highlight-color: transparent;
    user-select: none;
}

.mobile-menu-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(185, 34, 75, 0.2), transparent);
    transition: left 0.6s ease;
    border-radius: 10px;
}

.mobile-menu-btn:hover::before {
    left: 100%;
}

.mobile-menu-btn:hover {
    background: rgba(185, 34, 75, 0.08);
    border-color: rgba(185, 34, 75, 0.2);
    transform: scale(1.05);
}

.mobile-menu-btn:active {
    transform: scale(0.95);
    background: rgba(185, 34, 75, 0.15);
}

.mobile-menu-btn span {
    width: 26px;
    height: 3px;
    background: linear-gradient(45deg, #333, #555);
    margin: 3px 0;
    transition: all 0.5s cubic-bezier(0.68, -0.55, 0.265, 1.55);
    border-radius: 3px;
    position: relative;
    transform-origin: center;
    display: block;
}

.mobile-menu-btn:hover span {
    background: linear-gradient(45deg, #B9224B, #d63384);
    box-shadow: 0 0 8px rgba(185, 34, 75, 0.4);
    width: 28px;
}

.mobile-menu-btn.active {
    background: rgba(185, 34, 75, 0.12);
    border-color: rgba(185, 34, 75, 0.3);
}

.mobile-menu-btn.active span:nth-child(1) {
    transform: rotate(45deg) translate(7px, 7px);
    background: linear-gradient(45deg, #B9224B, #ff6b9d);
    box-shadow: 0 0 10px rgba(185, 34, 75, 0.5);
}

.mobile-menu-btn.active span:nth-child(2) {
    opacity: 0;
    transform: scale(0) rotate(180deg);
}

.mobile-menu-btn.active span:nth-child(3) {
    transform: rotate(-45deg) translate(7px, -7px);
    background: linear-gradient(45deg, #B9224B, #ff6b9d);
    box-shadow: 0 0 10px rgba(185, 34, 75, 0.5);
}

/* Enhanced Mobile Menu Overlay */
.mobile-menu-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100vh;
    height: 100dvh; /* Dynamic viewport height for mobile browsers */
    background: rgba(0, 0, 0, 0);
    backdrop-filter: blur(0px);
    -webkit-backdrop-filter: blur(0px);
    z-index: 9999;
    opacity: 0;
    visibility: hidden;
    transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
    overscroll-behavior: contain;
    -webkit-overflow-scrolling: touch;
}

.mobile-menu-overlay.active {
    opacity: 1;
    visibility: visible;
    background: rgba(0, 0, 0, 0.65);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
}

/* Enhanced Mobile Menu */
.mobile-menu {
    position: absolute;
    top: 0;
    right: 0;
    width: 320px;
    max-width: 85vw;
    height: 100vh;
    height: 100dvh; /* Dynamic viewport height */
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.96), rgba(248, 250, 252, 0.98));
    backdrop-filter: blur(30px);
    -webkit-backdrop-filter: blur(30px);
    padding: 0;
    transform: translateX(100%) scale(0.9);
    transition: all 0.7s cubic-bezier(0.68, -0.55, 0.265, 1.55);
    overflow-y: auto;
    overflow-x: hidden;
    overscroll-behavior: contain;
    -webkit-overflow-scrolling: touch;
    box-shadow:
        -20px 0 60px rgba(185, 34, 75, 0.12),
        -10px 0 30px rgba(0, 0, 0, 0.1),
        0 0 0 1px rgba(185, 34, 75, 0.08);
    border-left: 2px solid rgba(185, 34, 75, 0.15);
    border-radius: 20px 0 0 20px;
}

.mobile-menu-overlay.active .mobile-menu {
    transform: translateX(0) scale(1);
    box-shadow:
        -30px 0 80px rgba(185, 34, 75, 0.18),
        -15px 0 40px rgba(0, 0, 0, 0.15),
        0 0 0 1px rgba(185, 34, 75, 0.12);
}

.mobile-menu-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 24px;
    border-bottom: 1px solid rgba(185, 34, 75, 0.1);
}

.mobile-logo {
    width: 60px;
    height: 60px;
}

.mobile-logo img {
    width: 100%;
    height: 100%;
    object-fit: contain;
}

.mobile-close-btn {
    background: none;
    border: none;
    font-size: 32px;
    color: #333;
    cursor: pointer;
    padding: 8px;
    border-radius: 50%;
    transition: all 0.3s ease;
    width: 48px;
    height: 48px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.mobile-close-btn:hover {
    background: rgba(185, 34, 75, 0.1);
    color: #B9224B;
    transform: rotate(90deg);
}

/* Mobile Navigation Links */
.mobile-nav-links {
    list-style: none;
    padding: 20px 0;
    margin: 0;
}

.mobile-nav-links > li {
    margin: 0;
    opacity: 0;
    transform: translateX(50px);
    transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

.mobile-menu-overlay.active .mobile-nav-links > li {
    opacity: 1;
    transform: translateX(0);
}

.mobile-menu-overlay.active .mobile-nav-links > li:nth-child(1) { transition-delay: 0.1s; }
.mobile-menu-overlay.active .mobile-nav-links > li:nth-child(2) { transition-delay: 0.15s; }
.mobile-menu-overlay.active .mobile-nav-links > li:nth-child(3) { transition-delay: 0.2s; }
.mobile-menu-overlay.active .mobile-nav-links > li:nth-child(4) { transition-delay: 0.25s; }
.mobile-menu-overlay.active .mobile-nav-links > li:nth-child(5) { transition-delay: 0.3s; }
.mobile-menu-overlay.active .mobile-nav-links > li:nth-child(6) { transition-delay: 0.35s; }
.mobile-menu-overlay.active .mobile-nav-links > li:nth-child(7) { transition-delay: 0.4s; }

.mobile-nav-links > li > a {
    display: block;
    padding: 18px 24px;
    color: #333;
    text-decoration: none;
    font-weight: 500;
    font-size: 16px;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    border-left: 4px solid transparent;
    position: relative;
    overflow: hidden;
}

.mobile-nav-links > li > a::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(185, 34, 75, 0.1), transparent);
    transition: left 0.6s ease;
}

.mobile-nav-links > li > a:hover::before {
    left: 100%;
}

.mobile-nav-links > li > a:hover {
    background: linear-gradient(135deg, rgba(185, 34, 75, 0.08), rgba(185, 34, 75, 0.04));
    color: #B9224B;
    border-left-color: #B9224B;
    transform: translateX(12px) scale(1.02);
    box-shadow: 0 4px 15px rgba(185, 34, 75, 0.1);
}

/* Mobile Dropdown */
.mobile-dropdown {
    position: relative;
}

.mobile-dropdown-toggle {
    position: relative;
    padding-right: 50px !important;
}

.mobile-dropdown-toggle::after {
    content: '';
    position: absolute;
    right: 24px;
    top: 50%;
    transform: translateY(-50%);
    width: 0;
    height: 0;
    border-left: 5px solid transparent;
    border-right: 5px solid transparent;
    border-top: 5px solid #666;
    transition: all 0.3s ease;
}

.mobile-dropdown.active .mobile-dropdown-toggle::after {
    transform: translateY(-50%) rotate(180deg);
    border-top-color: #B9224B;
}

.mobile-dropdown-menu {
    list-style: none;
    padding: 0;
    margin: 0;
    max-height: 0;
    overflow: hidden;
    background: rgba(185, 34, 75, 0.02);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.mobile-dropdown.active .mobile-dropdown-menu {
    max-height: 300px;
    padding: 8px 0;
}

.mobile-dropdown-menu li a {
    display: block;
    padding: 12px 24px 12px 48px;
    color: #666;
    text-decoration: none;
    font-size: 14px;
    font-weight: 400;
    transition: all 0.3s ease;
    position: relative;
    
}

.mobile-dropdown-menu li a::before {
    content: '•';
    position: absolute;
    left: 36px;
    color: #B9224B;
    font-weight: bold;
}

.mobile-dropdown-menu li a:hover {
    background: rgba(185, 34, 75, 0.08);
    color: #B9224B;
    transform: translateX(8px);
}

/* Mobile Connect Section */
.mobile-connect-section {
    padding: 30px 24px 40px;
    border-top: 1px solid rgba(185, 34, 75, 0.1);
    margin-top: 20px;
    background: linear-gradient(135deg, rgba(185, 34, 75, 0.02), rgba(185, 34, 75, 0.05));
}

.mobile-connect-btn {
    margin-bottom: 25px;
}

.mobile-connect-btn .btn {
    background: linear-gradient(135deg, #B9224B, #d63384);
    border: none;
    padding: 15px 25px;
    font-weight: 600;
    font-size: 16px;
    border-radius: 12px;
    box-shadow: 0 8px 25px rgba(185, 34, 75, 0.3);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.mobile-connect-btn .btn:hover {
    background: linear-gradient(135deg, #d63384, #B9224B);
    transform: translateY(-3px);
    box-shadow: 0 12px 35px rgba(185, 34, 75, 0.4);
}

.mobile-connect-btn .btn:active {
    transform: translateY(-1px);
    box-shadow: 0 6px 20px rgba(185, 34, 75, 0.3);
}

/* Mobile Social Links */
.mobile-social-links {
    display: flex;
    justify-content: center;
    gap: 20px;
    flex-wrap: wrap;
}

.social-link {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 50px;
    height: 50px;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.9), rgba(248, 250, 252, 0.8));
    border: 2px solid rgba(185, 34, 75, 0.1);
    border-radius: 50%;
    color: #666;
    text-decoration: none;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    font-size: 18px;
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.social-link:hover {
    background: linear-gradient(135deg, #B9224B, #d63384);
    color: white;
    transform: translateY(-3px) scale(1.1);
    border-color: #B9224B;
    box-shadow: 0 8px 25px rgba(185, 34, 75, 0.4);
}

.social-link:active {
    transform: translateY(-1px) scale(1.05);
}

/* Enhanced Mobile Menu Button Icons */
.mobile-menu-btn {
    position: relative;
}

.hamburger-icon,
.close-icon {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 20px;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    color: #333;
}

.hamburger-icon {
    opacity: 1;
    visibility: visible;
}

.close-icon {
    opacity: 0;
    visibility: hidden;
    transform: translate(-50%, -50%) rotate(180deg);
}

.mobile-menu-btn.active .hamburger-icon {
    opacity: 0;
    visibility: hidden;
    transform: translate(-50%, -50%) rotate(-180deg);
}

.mobile-menu-btn.active .close-icon {
    opacity: 1;
    visibility: visible;
    transform: translate(-50%, -50%) rotate(0deg);
    color: #B9224B;
}

.mobile-menu-btn:hover .hamburger-icon,
.mobile-menu-btn:hover .close-icon {
    color: #B9224B;
}

/* Hide hamburger lines when using icons */
.mobile-menu-btn .hamburger-line {
    opacity: 0;
    visibility: hidden;
}

/* Enhanced Responsive Design */
@media screen and (max-width: 1024px) {
    .nav-links {
        gap: 1.5rem;
    }

    nav {
        padding: 0 30px;
    }
}

@media screen and (max-width: 768px) {
    nav {
        padding: 0 20px;
        height: 75px;
    }

    .nav-links,
    .btn {
        display: none;
    }

    .mobile-menu-btn {
        display: flex;
        order: 3;
    }

    .logo {
        width: 75px;
        height: 75px;
        order: 1;
    }

    .container {
        padding: 0;
    }
}

@media screen and (max-width: 640px) {
    nav {
        padding: 0 16px;
        height: 70px;
    }

    .logo {
        width: 65px;
        height: 65px;
    }

    .mobile-menu {
        width: 85%;
        max-width: 320px;
    }
}

@media screen and (max-width: 480px) {
    nav {
        padding: 0 12px;
        height: 65px;
    }

    .logo {
        width: 80px;
        height: 80px;
    }

    .mobile-menu {
        width: 100%;
        border-radius: 0;
    }

    .mobile-menu-btn {
        padding: 8px;
        min-width: 44px;
        min-height: 44px;
    }

    .mobile-menu-btn span {
        width: 22px;
        height: 2.5px;
        margin: 2.5px 0;
    }
}

@media screen and (max-width: 360px) {
    nav {
        padding: 0 10px;
        height: 60px;
    }

    .logo {
        width: 55px;
        height: 55px;
    }

    .mobile-menu-btn {
        padding: 6px;
        min-width: 40px;
        min-height: 40px;
    }

    .mobile-menu-btn span {
        width: 20px;
        height: 2px;
        margin: 2px 0;
    }
}

@media screen and (min-width: 769px) {
    .mobile-menu-btn,
    .mobile-menu-overlay {
        display: none !important;
    }

    .btn {
        display: block;
    }
}

/* Enhanced Desktop Styles */
@media screen and (min-width: 1200px) {
    nav {
        padding: 0 80px;
    }

    .nav-links {
        gap: 3rem;
    }

    .logo {
        width: 120px;
        height: 120px;
    }
}
.btn a{
    color: white;
    background-color: #B9224B;
    padding: 10px 20px;
    border-radius: 5px;
    text-decoration: none;
    font-weight: 500;
    transition: all 0.3s ease;
}
.btn a:hover{
    background-color: #d12a57;
    color: #fdfdfd;
}

/* Mobile Menu Button Focus and Accessibility */
.mobile-menu-btn:focus {
    outline: 2px solid #B9224B;
    outline-offset: 2px;
}

.mobile-menu-btn:focus:not(:focus-visible) {
    outline: none;
}

/* Smooth scrolling for mobile menu */
.mobile-menu {
    scroll-behavior: smooth;
}

/* Prevent text selection on mobile menu button */
.mobile-menu-btn,
.mobile-menu-btn * {
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .mobile-menu-btn span {
        background: #000;
    }

    .mobile-menu-btn.active span {
        background: #B9224B;
    }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
    .mobile-menu-btn,
    .mobile-menu-btn span,
    .mobile-menu-overlay,
    .mobile-menu,
    .mobile-nav-links > li {
        transition-duration: 0.2s;
    }
}