<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mobile Menu Test - Dwelling Modern Platform</title>

    <!-- Bootstrap 5.3.2 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet" integrity="sha384-T3c6CoIi6uLrA9TneNEoa7RxnatzjcDSCmG1MXxSR1GAsXEV/Dwwykc2MPK8M2HN" crossorigin="anonymous">

    <!-- Font Awesome 6.5.1 -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css" integrity="sha512-DTOQO9RWCH3ppGqcWaEA1BIZOC6xxalwEsw9c2QQeAIftl+Vegovlnee1c9QX4TctnWMn13TZye+giMm8e2LwA==" crossorigin="anonymous" referrerpolicy="no-referrer">

    <!-- Custom Styles -->
    <link rel="stylesheet" href="css/style.css">
    <style>
        /* Additional test styles */
        .test-content {
            padding: 40px 20px;
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .test-section {
            background: white;
            padding: 30px;
            margin: 20px 0;
            border-radius: 12px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }
        
        .test-section h2 {
            color: #B9224B;
            margin-bottom: 15px;
        }
        
        .test-section p {
            line-height: 1.6;
            color: #666;
            margin-bottom: 15px;
        }
        
        .device-info {
            background: #f8fafc;
            padding: 15px;
            border-radius: 8px;
            font-family: monospace;
            font-size: 14px;
            margin: 20px 0;
        }
        
        .test-buttons {
            display: flex;
            gap: 15px;
            flex-wrap: wrap;
            margin: 20px 0;
        }
        
        .test-btn {
            padding: 10px 20px;
            background: #B9224B;
            color: white;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .test-btn:hover {
            background: #d12a57;
            transform: translateY(-2px);
        }
    </style>
</head>
<body>
    <div class="container">
        <nav>
            <div class="logo">
                <img src="assets/logo.png" alt="Logo" onerror="this.style.display='none'; this.nextElementSibling.style.display='block';">
                <div style="display:none; width:100%; height:100%; background:#B9224B; border-radius:50%; display:flex; align-items:center; justify-content:center; color:white; font-weight:bold; font-size:24px;">D</div>
            </div>
            <ul class="nav-links">
                <li><a href="#home">Home</a></li>
                <li><a href="#about">About Us</a></li>
                <li class="dropdown">
                    <a href="#" class="dropdown-toggle">Services</a>
                    <ul class="dropdown-menu">
                        <li><a href="#buy">Buy Property</a></li>
                        <li><a href="#lease">Lease Property</a></li>
                        <li><a href="#pre-lease">Pre-Lease</a></li>
                        <li><a href="#sell">Sell Property</a></li>
                        <li><a href="#investment">Investment</a></li>
                    </ul>
                </li>
                <li class="dropdown">
                    <a href="#" class="dropdown-toggle">Projects</a>
                    <ul class="dropdown-menu">
                        <li><a href="#new-launch">New Launch</a></li>
                        <li><a href="#resale">Resale</a></li>
                    </ul>
                </li>
                <li><a href="#insight">Insight</a></li>
                <li><a href="#calculator">Calculator</a></li>
                <li><a href="#career">Career</a></li>
            </ul>
            <div class="btn">
                <a href="#connect">Let's Connect</a>
            </div>
            
            <!-- Enhanced Mobile Menu Button -->
            <div class="mobile-menu-btn" id="mobileMenuBtn" aria-label="Toggle mobile menu">
                <span class="hamburger-line"></span>
                <span class="hamburger-line"></span>
                <span class="hamburger-line"></span>
                <i class="fas fa-bars hamburger-icon"></i>
                <i class="fas fa-times close-icon"></i>
            </div>

            <!-- Mobile Menu Overlay -->
            <div class="mobile-menu-overlay" id="mobileMenuOverlay">
                <div class="mobile-menu">
                    <div class="mobile-menu-header">
                        <div class="mobile-logo">
                            <img src="assets/logo.png" alt="Logo" onerror="this.style.display='none'; this.nextElementSibling.style.display='block';">
                            <div style="display:none; width:100%; height:100%; background:#B9224B; border-radius:50%; display:flex; align-items:center; justify-content:center; color:white; font-weight:bold; font-size:18px;">D</div>
                        </div>
                        <button class="mobile-close-btn" id="mobileCloseBtn">&times;</button>
                    </div>
                    <ul class="mobile-nav-links">
                        <li><a href="#home">Home</a></li>
                        <li><a href="#about">About Us</a></li>
                        <li class="mobile-dropdown">
                            <a href="#" class="mobile-dropdown-toggle">Services</a>
                            <ul class="mobile-dropdown-menu">
                                <li><a href="#buy">Buy Property</a></li>
                                <li><a href="#lease">Lease Property</a></li>
                                <li><a href="#pre-lease">Pre-Lease</a></li>
                                <li><a href="#sell">Sell Property</a></li>
                                <li><a href="#investment">Investment</a></li>
                            </ul>
                        </li>
                        <li class="mobile-dropdown">
                            <a href="#" class="mobile-dropdown-toggle">Projects</a>
                            <ul class="mobile-dropdown-menu">
                                <li><a href="#new-launch">New Launch</a></li>
                                <li><a href="#resale">Resale</a></li>
                            </ul>
                        </li>
                        <li><a href="#insight"><i class="fas fa-lightbulb me-2"></i>Insight</a></li>
                        <li><a href="#calculator"><i class="fas fa-calculator me-2"></i>Calculator</a></li>
                        <li><a href="#career"><i class="fas fa-briefcase me-2"></i>Career</a></li>
                    </ul>

                    <!-- Mobile Connect Section -->
                    <div class="mobile-connect-section">
                        <div class="mobile-connect-btn">
                            <a href="#connect" class="btn btn-primary w-100">
                                <i class="fas fa-phone me-2"></i>Let's Connect
                            </a>
                        </div>
                        <div class="mobile-social-links">
                            <a href="#" class="social-link" aria-label="Facebook">
                                <i class="fab fa-facebook-f"></i>
                            </a>
                            <a href="#" class="social-link" aria-label="Twitter">
                                <i class="fab fa-twitter"></i>
                            </a>
                            <a href="#" class="social-link" aria-label="Instagram">
                                <i class="fab fa-instagram"></i>
                            </a>
                            <a href="#" class="social-link" aria-label="LinkedIn">
                                <i class="fab fa-linkedin-in"></i>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </nav>
    </div>

    <div class="test-content">
        <div class="test-section">
            <h2>Mobile Menu Responsiveness Test</h2>
            <p>This page demonstrates the enhanced mobile menu functionality. The mobile menu button is now fully responsive and works across all device sizes.</p>
            
            <div class="device-info" id="deviceInfo">
                <div>Screen Width: <span id="screenWidth"></span>px</div>
                <div>Screen Height: <span id="screenHeight"></span>px</div>
                <div>Device Type: <span id="deviceType"></span></div>
                <div>Touch Support: <span id="touchSupport"></span></div>
            </div>
            
            <div class="test-buttons">
                <button class="test-btn" onclick="testMobileMenu()">Test Mobile Menu</button>
                <button class="test-btn" onclick="testResponsiveness()">Test Responsiveness</button>
                <button class="test-btn" onclick="simulateResize()">Simulate Resize</button>
            </div>
        </div>

        <div class="test-section">
            <h2>Features Implemented</h2>
            <ul style="line-height: 1.8; color: #666;">
                <li>✅ Responsive mobile menu button with smooth animations</li>
                <li>✅ Touch-friendly button size (minimum 44px tap target)</li>
                <li>✅ Hamburger to X animation on menu toggle</li>
                <li>✅ Backdrop blur overlay with smooth transitions</li>
                <li>✅ Slide-in menu from right with scale animation</li>
                <li>✅ Auto-close on window resize above 768px</li>
                <li>✅ Escape key support to close menu</li>
                <li>✅ Click outside to close functionality</li>
                <li>✅ Swipe gesture support for closing</li>
                <li>✅ Accessibility improvements (ARIA labels, focus management)</li>
                <li>✅ Dropdown menus within mobile menu</li>
                <li>✅ Auto-close menu when clicking navigation links</li>
                <li>✅ Vibration feedback on supported devices</li>
                <li>✅ Smooth scrolling and overflow handling</li>
                <li>✅ Support for reduced motion preferences</li>
            </ul>
        </div>

        <div class="test-section">
            <h2>Responsive Breakpoints</h2>
            <ul style="line-height: 1.8; color: #666;">
                <li><strong>Desktop (1200px+):</strong> Full navigation with larger spacing</li>
                <li><strong>Tablet (1024px-1199px):</strong> Reduced navigation spacing</li>
                <li><strong>Mobile Large (769px-1023px):</strong> Mobile menu activated</li>
                <li><strong>Mobile Medium (641px-768px):</strong> Optimized mobile menu</li>
                <li><strong>Mobile Small (481px-640px):</strong> Compact mobile menu (85% width)</li>
                <li><strong>Mobile XSmall (361px-480px):</strong> Full-width mobile menu</li>
                <li><strong>Mobile XXSmall (≤360px):</strong> Ultra-compact design</li>
            </ul>
        </div>
    </div>

    <!-- Bootstrap 5.3.2 JavaScript Bundle -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js" integrity="sha384-C6RzsynM9kWDrMNeT87bh95OGNyZPhcTNXj1NW7RuBCsyN/o0jlpcV8Qyq46cDfL" crossorigin="anonymous"></script>

    <!-- Custom Mobile Menu Script -->
    <script src="js/mobile-menu.js"></script>
    <script>
        // Test functionality
        function updateDeviceInfo() {
            document.getElementById('screenWidth').textContent = window.innerWidth;
            document.getElementById('screenHeight').textContent = window.innerHeight;
            
            let deviceType = 'Desktop';
            if (window.innerWidth <= 360) deviceType = 'Mobile XXSmall';
            else if (window.innerWidth <= 480) deviceType = 'Mobile XSmall';
            else if (window.innerWidth <= 640) deviceType = 'Mobile Small';
            else if (window.innerWidth <= 768) deviceType = 'Mobile Medium';
            else if (window.innerWidth <= 1024) deviceType = 'Mobile Large/Tablet';
            else if (window.innerWidth <= 1199) deviceType = 'Tablet Large';
            
            document.getElementById('deviceType').textContent = deviceType;
            document.getElementById('touchSupport').textContent = 'ontouchstart' in window ? 'Yes' : 'No';
        }

        function testMobileMenu() {
            const btn = document.getElementById('mobileMenuBtn');
            if (btn.style.display === 'none' || window.innerWidth > 768) {
                alert('Mobile menu is only visible on screens 768px and below. Try resizing your browser window or using developer tools to simulate a mobile device.');
            } else {
                btn.click();
            }
        }

        function testResponsiveness() {
            alert('Try resizing your browser window or using developer tools (F12) to test different screen sizes. The mobile menu will automatically show/hide based on screen width.');
        }

        function simulateResize() {
            // This would typically be done through developer tools
            alert('Use browser developer tools (F12) → Toggle device toolbar to simulate different screen sizes and test responsiveness.');
        }

        // Update device info on load and resize
        updateDeviceInfo();
        window.addEventListener('resize', updateDeviceInfo);
    </script>
</body>
</html>
