// Enhanced Mobile Menu Functionality
document.addEventListener('DOMContentLoaded', function() {
    const mobileMenuBtn = document.getElementById('mobileMenuBtn');
    const mobileMenuOverlay = document.getElementById('mobileMenuOverlay');
    const mobileCloseBtn = document.getElementById('mobileCloseBtn');
    const mobileDropdownToggles = document.querySelectorAll('.mobile-dropdown-toggle');
    const mobileNavLinks = document.querySelectorAll('.mobile-nav-links > li > a:not(.mobile-dropdown-toggle)');

    // Function to close mobile menu
    function closeMobileMenu() {
        mobileMenuBtn.classList.remove('active');
        mobileMenuOverlay.classList.remove('active');
        document.body.style.overflow = 'auto';

        // Close all dropdowns when menu closes
        mobileDropdownToggles.forEach(toggle => {
            toggle.parentElement.classList.remove('active');
        });
    }

    // Function to open mobile menu
    function openMobileMenu() {
        mobileMenuBtn.classList.add('active');
        mobileMenuOverlay.classList.add('active');
        document.body.style.overflow = 'hidden';

        // Add vibration effect if supported
        if (navigator.vibrate) {
            navigator.vibrate(50);
        }
    }

    // Toggle mobile menu
    mobileMenuBtn.addEventListener('click', (e) => {
        e.preventDefault();
        e.stopPropagation();

        if (mobileMenuOverlay.classList.contains('active')) {
            closeMobileMenu();
        } else {
            openMobileMenu();
        }
    });

    // Close mobile menu
    mobileCloseBtn.addEventListener('click', (e) => {
        e.preventDefault();
        closeMobileMenu();
    });

    // Close menu when clicking overlay (not the menu itself)
    mobileMenuOverlay.addEventListener('click', (e) => {
        if (e.target === mobileMenuOverlay) {
            closeMobileMenu();
        }
    });

    // Mobile dropdown functionality
    mobileDropdownToggles.forEach(toggle => {
        toggle.addEventListener('click', (e) => {
            e.preventDefault();
            e.stopPropagation();

            const parent = toggle.parentElement;
            const isActive = parent.classList.contains('active');

            // Close other dropdowns
            mobileDropdownToggles.forEach(otherToggle => {
                if (otherToggle !== toggle) {
                    otherToggle.parentElement.classList.remove('active');
                }
            });

            // Toggle current dropdown
            if (isActive) {
                parent.classList.remove('active');
            } else {
                parent.classList.add('active');
            }
        });
    });

    // Close menu when clicking on regular nav links (not dropdowns)
    mobileNavLinks.forEach(link => {
        link.addEventListener('click', () => {
            // Add small delay for better UX
            setTimeout(() => {
                closeMobileMenu();
            }, 150);
        });
    });

    // Handle escape key to close menu
    document.addEventListener('keydown', (e) => {
        if (e.key === 'Escape' && mobileMenuOverlay.classList.contains('active')) {
            closeMobileMenu();
        }
    });

    // Enhanced resize handler with debouncing
    let resizeTimeout;
    window.addEventListener('resize', () => {
        clearTimeout(resizeTimeout);
        resizeTimeout = setTimeout(() => {
            if (window.innerWidth > 768) {
                closeMobileMenu();
            }
        }, 100);
    });

    // Prevent menu from staying open on orientation change
    window.addEventListener('orientationchange', () => {
        setTimeout(() => {
            if (window.innerWidth > 768) {
                closeMobileMenu();
            }
        }, 500);
    });

    // Touch event handling for better mobile experience
    let touchStartY = 0;
    let touchEndY = 0;

    mobileMenuOverlay.addEventListener('touchstart', (e) => {
        touchStartY = e.changedTouches[0].screenY;
    });

    mobileMenuOverlay.addEventListener('touchend', (e) => {
        touchEndY = e.changedTouches[0].screenY;

        // Close menu on swipe right (if swiping on overlay, not menu)
        if (e.target === mobileMenuOverlay) {
            const swipeDistance = touchStartY - touchEndY;
            if (Math.abs(swipeDistance) > 50) {
                closeMobileMenu();
            }
        }
    });

    // Accessibility improvements
    mobileMenuBtn.setAttribute('aria-label', 'Toggle mobile menu');
    mobileMenuBtn.setAttribute('aria-expanded', 'false');

    // Update aria-expanded when menu state changes
    const observer = new MutationObserver((mutations) => {
        mutations.forEach((mutation) => {
            if (mutation.type === 'attributes' && mutation.attributeName === 'class') {
                const isActive = mobileMenuOverlay.classList.contains('active');
                mobileMenuBtn.setAttribute('aria-expanded', isActive.toString());
            }
        });
    });

    observer.observe(mobileMenuOverlay, { attributes: true });
});
